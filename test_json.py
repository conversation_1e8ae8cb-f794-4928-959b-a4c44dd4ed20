#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JSON文件的有效性
"""

import json
import sys

def test_json_file(filename):
    """测试JSON文件"""
    try:
        print(f"正在测试文件: {filename}")
        
        # 读取文件
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查BOM
        if content.startswith('\ufeff'):
            print("WARNING: 检测到BOM字符")
            content = content[1:]  # 移除BOM
        
        # 解析JSON
        data = json.loads(content)
        
        print("OK JSON文件有效")
        print(f"数据类型: {type(data)}")

        if isinstance(data, list):
            print(f"记录数量: {len(data)}")
            if len(data) > 0:
                first_record = data[0]
                print(f"第一条记录字段: {list(first_record.keys())}")
                print(f"第一条记录内容预览: {str(first_record)[:200]}...")

        # 创建清理后的文件
        clean_filename = filename.replace('.json', '_clean.json')
        with open(clean_filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        print(f"已创建清理后的文件: {clean_filename}")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"ERROR JSON解析错误: {e}")
        return False
    except Exception as e:
        print(f"ERROR 其他错误: {e}")
        return False

def main():
    """主函数"""
    filename = sys.argv[1] if len(sys.argv) > 1 else 'oa_data.json'
    test_json_file(filename)

if __name__ == "__main__":
    main()
