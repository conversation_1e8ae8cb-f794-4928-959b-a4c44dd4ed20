#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的HTTP服务器
用于本地访问可视化页面，解决跨域问题
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

def start_server(port=8000):
    """启动HTTP服务器"""
    try:
        # 确保在正确的目录中
        os.chdir(Path(__file__).parent)
        
        # 创建服务器
        
        # 添加CORS头部支持
        class CORSRequestHandler(http.server.SimpleHTTPRequestHandler):
            def end_headers(self):
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                self.send_header('Access-Control-Allow-Headers', '*')
                super().end_headers()
        
        with socketserver.TCPServer(("", port), CORSRequestHandler) as httpd:
            print(f"HTTP服务器已启动")
            print(f"服务器地址: http://localhost:{port}")
            print(f"可视化页面: http://localhost:{port}/oa_visualization.html")
            print(f"当前目录: {os.getcwd()}")
            print(f"按 Ctrl+C 停止服务器")
            print("-" * 50)

            # 检查文件是否存在
            files_to_check = ['oa_visualization.html', 'oa_data.json', 'oa_data_clean.json']
            for file in files_to_check:
                if os.path.exists(file):
                    print(f"OK {file} 存在")
                else:
                    print(f"MISSING {file} 不存在")

            print("-" * 50)

            # 自动打开浏览器
            try:
                webbrowser.open(f'http://localhost:{port}/oa_visualization.html')
                print("已自动打开浏览器")
            except:
                print("无法自动打开浏览器，请手动访问上述地址")
            
            print("\n等待连接...")
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"端口 {port} 已被占用，尝试使用端口 {port + 1}")
            start_server(port + 1)
        else:
            print(f"启动服务器失败: {e}")
    except KeyboardInterrupt:
        print("\n服务器已停止")

def main():
    """主函数"""
    port = 8000
    
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("端口号必须是数字")
            return

    print("OA数据可视化服务器")
    print("=" * 30)
    start_server(port)

if __name__ == "__main__":
    main()
