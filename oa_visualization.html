<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OA数据可视化</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 1.1rem;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .control-group label {
            font-weight: 600;
            color: #555;
        }

        input, select, button {
            padding: 10px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        input:focus, select:focus {
            outline: none;
            border-color: #667eea;
        }

        button {
            background: #667eea;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: 600;
            transition: background 0.3s ease;
        }

        button:hover {
            background: #5a6fd8;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .chart-container {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
            text-align: center;
        }

        .data-table {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-header {
            background: #667eea;
            color: white;
            padding: 20px;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .table-content {
            max-height: 400px;
            overflow-y: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
            position: sticky;
            top: 0;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .chart-container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 OA数据可视化分析</h1>
            <p>汕头大学OA系统数据统计与分析</p>
            <p style="font-size: 0.9em; opacity: 0.8;">💡 为了优化性能，图表分析基于数据采样显示</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalRecords">0</div>
                <div class="stat-label">总记录数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalPages">0</div>
                <div class="stat-label">总页数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avgContentLength">0</div>
                <div class="stat-label">平均内容长度</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="lastUpdate">--</div>
                <div class="stat-label">最后更新</div>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <label for="searchInput">搜索内容</label>
                <input type="text" id="searchInput" placeholder="输入关键词搜索...">
            </div>
            <div class="control-group">
                <label for="pageFilter">页面筛选</label>
                <select id="pageFilter">
                    <option value="">所有页面</option>
                </select>
            </div>
            <div class="control-group">
                <label>&nbsp;</label>
                <button onclick="loadData()">加载数据</button>
            </div>
            <div class="control-group">
                <label>&nbsp;</label>
                <input type="file" id="fileInput" accept=".json" style="display: none;" onchange="loadFromFile()">
                <button onclick="document.getElementById('fileInput').click()">选择文件</button>
            </div>
            <div class="control-group">
                <label>&nbsp;</label>
                <button onclick="exportData()">导出数据</button>
            </div>
        </div>

        <div class="charts-grid">
            <div class="chart-container">
                <div class="chart-title">页面数据分布</div>
                <canvas id="pageChart"></canvas>
            </div>
            <div class="chart-container">
                <div class="chart-title">内容长度分布</div>
                <canvas id="lengthChart"></canvas>
            </div>
            <div class="chart-container">
                <div class="chart-title">时间分布</div>
                <canvas id="timeChart"></canvas>
            </div>
            <div class="chart-container">
                <div class="chart-title">关键词统计</div>
                <canvas id="keywordChart"></canvas>
            </div>
        </div>

        <div class="data-table">
            <div class="table-header">
                📋 数据详情 (显示前50条，共 <span id="filteredCount">0</span> 条记录)
            </div>
            <div class="table-content">
                <table>
                    <thead>
                        <tr>
                            <th>页码</th>
                            <th>内容预览</th>
                            <th>内容长度</th>
                            <th>时间戳</th>
                        </tr>
                    </thead>
                    <tbody id="dataTableBody">
                        <tr>
                            <td colspan="4" class="loading">正在加载数据...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        let allData = [];
        let filteredData = [];
        let charts = {};

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            loadSampleData();
            setupEventListeners();
        });

        function setupEventListeners() {
            document.getElementById('searchInput').addEventListener('input', filterData);
            document.getElementById('pageFilter').addEventListener('change', filterData);
        }

        function initializeCharts() {
            try {
                // 页面分布图
                const pageCtx = document.getElementById('pageChart').getContext('2d');
                charts.pageChart = new Chart(pageCtx, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: '记录数',
                        data: [],
                        backgroundColor: 'rgba(102, 126, 234, 0.8)',
                        borderColor: 'rgba(102, 126, 234, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 内容长度分布图
            const lengthCtx = document.getElementById('lengthChart').getContext('2d');
            charts.lengthChart = new Chart(lengthCtx, {
                type: 'histogram',
                data: {
                    labels: ['0-100', '101-500', '501-1000', '1001-2000', '2000+'],
                    datasets: [{
                        label: '记录数',
                        data: [0, 0, 0, 0, 0],
                        backgroundColor: 'rgba(118, 75, 162, 0.8)',
                        borderColor: 'rgba(118, 75, 162, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 时间分布图
            const timeCtx = document.getElementById('timeChart').getContext('2d');
            charts.timeChart = new Chart(timeCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '抓取数量',
                        data: [],
                        borderColor: 'rgba(255, 99, 132, 1)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 关键词统计图
            const keywordCtx = document.getElementById('keywordChart').getContext('2d');
            charts.keywordChart = new Chart(keywordCtx, {
                type: 'doughnut',
                data: {
                    labels: [],
                    datasets: [{
                        data: [],
                        backgroundColor: [
                            '#FF6384',
                            '#36A2EB',
                            '#FFCE56',
                            '#4BC0C0',
                            '#9966FF',
                            '#FF9F40'
                        ]
                    }]
                },
                options: {
                    responsive: true
                }
            });
            } catch (error) {
                console.error('图表初始化失败:', error);
                showNotification('图表初始化失败: ' + error.message, 'error');
            }
        }

        function loadSampleData() {
            // 生成示例数据
            const sampleData = [];
            for (let i = 1; i <= 100; i++) {
                sampleData.push({
                    page: Math.floor((i - 1) / 10) + 1,
                    content: `这是第${i}条示例数据内容，包含一些关键信息和描述文字。`,
                    html: `<div>示例HTML内容${i}</div>`,
                    timestamp: new Date(Date.now() - Math.random() * 86400000 * 30).toISOString()
                });
            }

            allData = sampleData;
            filteredData = [...allData];
            updateDisplay();
        }

        async function loadData() {
            // 尝试加载多个可能的数据文件
            const filesToTry = ['oa_data_clean.json', 'oa_data.json'];

            for (const filename of filesToTry) {
                try {
                    console.log(`尝试加载文件: ${filename}`);
                    const response = await fetch(filename);

                    if (response.ok) {
                        let text = await response.text();

                        // 移除BOM字符
                        if (text.charCodeAt(0) === 0xFEFF) {
                            text = text.slice(1);
                        }

                        const data = JSON.parse(text);

                        // 验证数据格式
                        if (!Array.isArray(data)) {
                            throw new Error('数据格式错误：应该是数组格式');
                        }

                        allData = data;
                        filteredData = [...allData];
                        updateDisplay();
                        showNotification(`数据加载成功！从 ${filename} 加载了 ${data.length} 条记录`, 'success');
                        return; // 成功加载，退出函数
                    }
                } catch (error) {
                    console.error(`加载 ${filename} 失败:`, error);
                    continue; // 尝试下一个文件
                }
            }

            // 所有文件都加载失败
            showNotification('无法加载数据文件。请使用以下方法之一：<br>1. 确保服务器正在运行：python start_server.py<br>2. 拖拽JSON文件到页面<br>3. 使用文件选择器', 'error');
        }

        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.innerHTML = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 600;
                z-index: 1000;
                max-width: 400px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                transition: all 0.3s ease;
                ${type === 'success' ? 'background: #4CAF50;' :
                  type === 'error' ? 'background: #f44336;' :
                  'background: #2196F3;'}
            `;

            document.body.appendChild(notification);

            // 自动移除
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 5000);
        }

        function filterData() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const pageFilter = document.getElementById('pageFilter').value;

            filteredData = allData.filter(record => {
                const matchesSearch = !searchTerm || record.content.toLowerCase().includes(searchTerm);
                const matchesPage = !pageFilter || record.page.toString() === pageFilter;
                return matchesSearch && matchesPage;
            });

            updateDisplay();
        }

        function updateDisplay() {
            // 显示加载指示器
            showLoadingIndicator();

            // 使用setTimeout分批处理，避免阻塞UI
            setTimeout(() => {
                updateStats();
                setTimeout(() => {
                    updateCharts();
                    setTimeout(() => {
                        updateTable();
                        setTimeout(() => {
                            updatePageFilter();
                            hideLoadingIndicator();
                        }, 10);
                    }, 10);
                }, 10);
            }, 10);
        }

        function showLoadingIndicator() {
            const indicator = document.createElement('div');
            indicator.id = 'loadingIndicator';
            indicator.innerHTML = '正在更新数据...';
            indicator.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 20px;
                border-radius: 8px;
                z-index: 9999;
                font-size: 16px;
            `;
            document.body.appendChild(indicator);
        }

        function hideLoadingIndicator() {
            const indicator = document.getElementById('loadingIndicator');
            if (indicator) {
                indicator.remove();
            }
        }

        function updateStats() {
            document.getElementById('totalRecords').textContent = allData.length.toLocaleString();
            document.getElementById('totalPages').textContent = Math.max(...allData.map(r => r.page));

            const avgLength = allData.reduce((sum, r) => sum + r.content.length, 0) / allData.length;
            document.getElementById('avgContentLength').textContent = Math.round(avgLength);

            const latestTime = new Date(Math.max(...allData.map(r => new Date(r.timestamp))));
            document.getElementById('lastUpdate').textContent = latestTime.toLocaleDateString();
        }

        function updateCharts() {
            try {
                updatePageChart();
                updateLengthChart();
                updateTimeChart();
                updateKeywordChart();
            } catch (error) {
                console.error('更新图表时出错:', error);
                showNotification('图表更新失败: ' + error.message, 'error');
            }
        }

        function updatePageChart() {
            if (!charts.pageChart) {
                console.warn('页面图表未初始化');
                return;
            }

            // 为了性能，只处理前2000条数据用于图表显示
            const sampleData = filteredData.slice(0, 2000);
            const pageStats = {};
            sampleData.forEach(record => {
                pageStats[record.page] = (pageStats[record.page] || 0) + 1;
            });

            const pages = Object.keys(pageStats).sort((a, b) => parseInt(a) - parseInt(b));
            const counts = pages.map(page => pageStats[page]);

            charts.pageChart.data.labels = pages.slice(0, 20); // 只显示前20页
            charts.pageChart.data.datasets[0].data = counts.slice(0, 20);
            charts.pageChart.update();
        }

        function updateLengthChart() {
            if (!charts.lengthChart) {
                console.warn('长度图表未初始化');
                return;
            }

            // 为了性能，只处理前2000条数据
            const sampleData = filteredData.slice(0, 2000);
            const lengthBuckets = [0, 0, 0, 0, 0];
            sampleData.forEach(record => {
                const length = record.content.length;
                if (length <= 100) lengthBuckets[0]++;
                else if (length <= 500) lengthBuckets[1]++;
                else if (length <= 1000) lengthBuckets[2]++;
                else if (length <= 2000) lengthBuckets[3]++;
                else lengthBuckets[4]++;
            });

            charts.lengthChart.data.datasets[0].data = lengthBuckets;
            charts.lengthChart.update();
        }

        function updateTimeChart() {
            if (!charts.timeChart) {
                console.warn('时间图表未初始化');
                return;
            }

            // 为了性能，只处理前2000条数据
            const sampleData = filteredData.slice(0, 2000);
            const timeStats = {};
            sampleData.forEach(record => {
                const date = new Date(record.timestamp).toDateString();
                timeStats[date] = (timeStats[date] || 0) + 1;
            });

            const dates = Object.keys(timeStats).sort();
            const counts = dates.map(date => timeStats[date]);

            charts.timeChart.data.labels = dates.slice(-14); // 最近14天
            charts.timeChart.data.datasets[0].data = counts.slice(-14);
            charts.timeChart.update();
        }

        function updateKeywordChart() {
            if (!charts.keywordChart) {
                console.warn('关键词图表未初始化');
                return;
            }

            // 为了性能，只处理前500条数据进行关键词分析
            const sampleData = filteredData.slice(0, 500);
            const keywords = {};
            const commonWords = ['的', '是', '在', '有', '和', '了', '与', '为', '等', '及', '汕头大学', 'OA', '发布单位', '全部', '全文检索'];

            sampleData.forEach(record => {
                const words = record.content.match(/[\u4e00-\u9fa5]{2,}/g) || [];
                words.forEach(word => {
                    if (!commonWords.includes(word) && word.length >= 2) {
                        keywords[word] = (keywords[word] || 0) + 1;
                    }
                });
            });

            const sortedKeywords = Object.entries(keywords)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 6);

            charts.keywordChart.data.labels = sortedKeywords.map(k => k[0]);
            charts.keywordChart.data.datasets[0].data = sortedKeywords.map(k => k[1]);
            charts.keywordChart.update();
        }

        function updateTable() {
            const tbody = document.getElementById('dataTableBody');
            const filteredCount = document.getElementById('filteredCount');

            filteredCount.textContent = filteredData.length;

            if (filteredData.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4" style="text-align: center; color: #666;">没有找到匹配的数据</td></tr>';
                return;
            }

            const rows = filteredData.slice(0, 50).map(record => `
                <tr>
                    <td>${record.page}</td>
                    <td title="${record.content}">${record.content.substring(0, 100)}${record.content.length > 100 ? '...' : ''}</td>
                    <td>${record.content.length}</td>
                    <td>${new Date(record.timestamp).toLocaleString()}</td>
                </tr>
            `).join('');

            tbody.innerHTML = rows;
        }

        function updatePageFilter() {
            const select = document.getElementById('pageFilter');
            const currentValue = select.value;

            const pages = [...new Set(allData.map(r => r.page))].sort((a, b) => a - b);

            select.innerHTML = '<option value="">所有页面</option>' +
                pages.map(page => `<option value="${page}">第${page}页</option>`).join('');

            select.value = currentValue;
        }

        function loadFromFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        let content = e.target.result;
                        // 移除BOM字符
                        if (content.charCodeAt(0) === 0xFEFF) {
                            content = content.slice(1);
                        }

                        const data = JSON.parse(content);

                        // 验证数据格式
                        if (!Array.isArray(data)) {
                            throw new Error('数据格式错误：应该是数组格式');
                        }

                        if (data.length === 0) {
                            throw new Error('数据为空');
                        }

                        // 验证数据结构
                        const firstRecord = data[0];
                        if (!firstRecord.hasOwnProperty('page') || !firstRecord.hasOwnProperty('content')) {
                            throw new Error('数据结构错误：缺少必要字段');
                        }

                        allData = data;
                        filteredData = [...allData];
                        updateDisplay();
                        showNotification('文件加载成功！共加载 ' + data.length + ' 条记录', 'success');
                    } catch (error) {
                        console.error('文件加载错误:', error);
                        showNotification('文件格式错误：' + error.message, 'error');
                    }
                };
                reader.readAsText(file, 'UTF-8');
            }
        }

        function exportData() {
            const dataStr = JSON.stringify(filteredData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `oa_filtered_data_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showNotification('数据导出成功！', 'success');
        }

        // 文件拖拽上传功能
        document.addEventListener('dragover', function(e) {
            e.preventDefault();
        });

        document.addEventListener('drop', function(e) {
            e.preventDefault();
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];
                if (file.name.endsWith('.json')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            let content = e.target.result;
                            // 移除BOM字符
                            if (content.charCodeAt(0) === 0xFEFF) {
                                content = content.slice(1);
                            }

                            const data = JSON.parse(content);

                            // 验证数据格式
                            if (!Array.isArray(data)) {
                                throw new Error('数据格式错误：应该是数组格式');
                            }

                            allData = data;
                            filteredData = [...allData];
                            updateDisplay();
                            showNotification('拖拽文件加载成功！共加载 ' + data.length + ' 条记录', 'success');
                        } catch (error) {
                            console.error('拖拽文件加载错误:', error);
                            showNotification('JSON文件格式错误：' + error.message, 'error');
                        }
                    };
                    reader.readAsText(file);
                } else {
                    showNotification('请拖拽JSON格式的数据文件', 'error');
                }
            }
        });
    </script>
</body>
</html>
