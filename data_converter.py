#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据转换工具
将爬取的CSV数据转换为可视化页面可用的JSON格式
"""

import csv
import json
import sys
from datetime import datetime

def csv_to_json(csv_file, json_file):
    """将CSV文件转换为JSON文件"""
    try:
        data = []
        
        with open(csv_file, 'r', encoding='utf-8-sig') as csvfile:
            reader = csv.DictReader(csvfile)
            
            for row in reader:
                # 确保数据格式正确
                record = {
                    'page': int(row.get('page', 0)),
                    'content': row.get('content', ''),
                    'html': row.get('html', ''),
                    'timestamp': row.get('timestamp', datetime.now().isoformat())
                }
                data.append(record)
        
        # 保存为JSON文件
        with open(json_file, 'w', encoding='utf-8') as jsonfile:
            json.dump(data, jsonfile, ensure_ascii=False, indent=2)
        
        print(f"转换完成！")
        print(f"输入文件: {csv_file}")
        print(f"输出文件: {json_file}")
        print(f"记录数量: {len(data)}")
        
        return True
        
    except Exception as e:
        print(f"转换失败: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python data_converter.py <csv_file> [json_file]")
        print("示例: python data_converter.py oa_data.csv oa_data.json")
        return
    
    csv_file = sys.argv[1]
    json_file = sys.argv[2] if len(sys.argv) > 2 else csv_file.replace('.csv', '.json')
    
    csv_to_json(csv_file, json_file)

if __name__ == "__main__":
    main()
