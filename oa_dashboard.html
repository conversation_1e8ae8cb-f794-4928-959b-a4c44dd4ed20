<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>汕头大学OA数据可视化仪表板</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/wordcloud2@1.2.2/wordcloud2.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .file-upload {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .upload-area {
            border: 3px dashed #3498db;
            border-radius: 10px;
            padding: 40px;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #2980b9;
            background: #e3f2fd;
        }

        .upload-area.dragover {
            border-color: #27ae60;
            background: #e8f5e8;
        }

        .upload-icon {
            font-size: 3em;
            color: #3498db;
            margin-bottom: 15px;
        }

        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .kpi-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .kpi-card:hover {
            transform: translateY(-5px);
        }

        .kpi-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .kpi-label {
            color: #7f8c8d;
            font-size: 1.1em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .chart-title {
            font-size: 1.4em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .chart-canvas {
            max-height: 400px;
        }

        .data-table-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .search-filter {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .search-input, .filter-select {
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .search-input:focus, .filter-select:focus {
            outline: none;
            border-color: #3498db;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .data-table tr:hover {
            background: #f8f9fa;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 16px;
            border: 1px solid #3498db;
            background: white;
            color: #3498db;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination button:hover,
        .pagination button.active {
            background: #3498db;
            color: white;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        .wordcloud-container {
            height: 400px;
            position: relative;
        }

        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .search-filter {
                flex-direction: column;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1>🏛️ 汕头大学OA数据可视化仪表板</h1>
            <p>Professional Data Analytics Dashboard - Powered by Advanced Visualization</p>
        </div>

        <!-- 文件上传区域 -->
        <div class="file-upload">
            <div class="upload-area" id="uploadArea">
                <div class="upload-icon">📁</div>
                <h3>拖拽或点击上传JSON数据文件</h3>
                <p>支持格式：JSON (.json)</p>
                <p style="font-size: 14px; color: #666; margin-top: 10px;">💡 如果文件太大无法加载，请尝试使用分块文件（oa_data_chunk_001.json等）</p>
                <input type="file" id="fileInput" accept=".json" style="display: none;">
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在处理数据，请稍候...</p>
        </div>

        <!-- 主要内容区域 -->
        <div id="mainContent" class="hidden">
            <!-- KPI指标卡片 -->
            <div class="kpi-grid">
                <div class="kpi-card">
                    <div class="kpi-value" id="totalDocs">0</div>
                    <div class="kpi-label">总文档数</div>
                </div>
                <div class="kpi-card">
                    <div class="kpi-value" id="totalUnits">0</div>
                    <div class="kpi-label">发布单位数</div>
                </div>
                <div class="kpi-card">
                    <div class="kpi-value" id="avgContentLength">0</div>
                    <div class="kpi-label">平均内容长度</div>
                </div>
                <div class="kpi-card">
                    <div class="kpi-value" id="latestDate">-</div>
                    <div class="kpi-label">最新发布日期</div>
                </div>
                <div class="kpi-card">
                    <div class="kpi-value" id="totalAuthors">0</div>
                    <div class="kpi-label">发布人数</div>
                </div>
            </div>

            <!-- 图表网格 -->
            <div class="charts-grid">
                <!-- 发布单位分布 -->
                <div class="chart-container">
                    <div class="chart-title">📊 发布单位分布</div>
                    <canvas id="unitChart" class="chart-canvas"></canvas>
                </div>

                <!-- 时间趋势 -->
                <div class="chart-container">
                    <div class="chart-title">📈 发布时间趋势</div>
                    <canvas id="timeChart" class="chart-canvas"></canvas>
                </div>

                <!-- 内容长度分布 -->
                <div class="chart-container">
                    <div class="chart-title">📏 内容长度分布</div>
                    <canvas id="lengthChart" class="chart-canvas"></canvas>
                </div>

                <!-- 词云图 -->
                <div class="chart-container">
                    <div class="chart-title">☁️ 关键词云</div>
                    <div id="wordcloud" class="wordcloud-container"></div>
                </div>
            </div>

            <!-- 数据表格 -->
            <div class="data-table-container">
                <div class="chart-title">📋 详细数据表格</div>
                
                <!-- 搜索和筛选 -->
                <div class="search-filter">
                    <input type="text" id="searchInput" class="search-input" placeholder="搜索文档标题或内容...">
                    <select id="unitFilter" class="filter-select">
                        <option value="">所有发布单位</option>
                    </select>
                    <select id="dateFilter" class="filter-select">
                        <option value="">所有时间</option>
                        <option value="7">最近7天</option>
                        <option value="30">最近30天</option>
                        <option value="90">最近90天</option>
                    </select>
                </div>

                <!-- 数据表格 -->
                <table class="data-table" id="dataTable">
                    <thead>
                        <tr>
                            <th>文档ID</th>
                            <th>标题</th>
                            <th>发布单位</th>
                            <th>发布人</th>
                            <th>发布日期</th>
                            <th>内容长度</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                    </tbody>
                </table>

                <!-- 分页 -->
                <div class="pagination" id="pagination"></div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let rawData = [];
        let filteredData = [];
        let currentPage = 1;
        const itemsPerPage = 10;
        let charts = {};

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeUpload();
        });

        // 文件上传初始化
        function initializeUpload() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');

            // 点击上传
            uploadArea.addEventListener('click', () => fileInput.click());

            // 文件选择
            fileInput.addEventListener('change', handleFileSelect);

            // 拖拽上传
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFile(files[0]);
                }
            });
        }

        // 处理文件选择
        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        // 处理文件
        function handleFile(file) {
            if (!file.name.endsWith('.json')) {
                alert('请选择JSON格式的文件！');
                return;
            }

            showLoading();

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const text = e.target.result;
                    console.log('File size:', Math.round(text.length / 1024), 'KB');

                    // 检查文件是否为空
                    if (!text || text.trim().length === 0) {
                        throw new Error('文件为空或无法读取');
                    }

                    console.log('First 500 chars:', text.substring(0, 500));
                    console.log('Last 500 chars:', text.substring(Math.max(0, text.length - 500)));

                    // 检查JSON格式基本结构
                    const trimmedText = text.trim();
                    if (!trimmedText.startsWith('[') && !trimmedText.startsWith('{')) {
                        throw new Error('文件不是有效的JSON格式（应以[或{开头）');
                    }

                    if (!trimmedText.endsWith(']') && !trimmedText.endsWith('}')) {
                        console.warn('JSON文件可能不完整（未以]或}结尾）');
                        // 尝试修复不完整的JSON数组
                        if (trimmedText.startsWith('[') && !trimmedText.endsWith(']')) {
                            console.log('尝试修复不完整的JSON数组...');
                            // 找到最后一个完整的对象
                            let fixedText = trimmedText;
                            const lastCommaIndex = fixedText.lastIndexOf(',');
                            if (lastCommaIndex > 0) {
                                fixedText = fixedText.substring(0, lastCommaIndex) + ']';
                                console.log('修复后的JSON长度:', fixedText.length);
                            } else {
                                fixedText = '[' + ']'; // 空数组
                            }
                            text = fixedText;
                        }
                    }

                    // 检查是否有循环引用或其他问题
                    let data;
                    try {
                        data = JSON.parse(text);
                    } catch (parseError) {
                        console.error('JSON parse error:', parseError);
                        console.log('Error position:', parseError.message);

                        // 尝试找到问题位置
                        const match = parseError.message.match(/position (\d+)/);
                        if (match) {
                            const pos = parseInt(match[1]);
                            console.log('Problematic text around position:', text.substring(Math.max(0, pos - 100), pos + 100));
                        }

                        // 如果是"Unexpected end of JSON input"，提供更具体的建议
                        if (parseError.message.includes('Unexpected end of JSON input')) {
                            throw new Error('JSON文件不完整，可能是生成过程中被中断。请重新运行爬虫程序生成完整的JSON文件。');
                        }

                        throw new Error('JSON解析失败: ' + parseError.message);
                    }

                    // 验证数据结构
                    if (!Array.isArray(data)) {
                        throw new Error('JSON文件应包含一个数组');
                    }

                    if (data.length === 0) {
                        throw new Error('JSON文件中没有数据');
                    }

                    console.log('Data loaded successfully, length:', data.length);
                    console.log('Sample data structure:', data[0]);

                    processData(data);
                } catch (error) {
                    console.error('File processing error:', error);
                    alert('文件处理错误：' + error.message + '\n\n建议：\n1. 检查JSON文件是否完整\n2. 重新运行爬虫程序\n3. 尝试使用较小的数据文件');
                    hideLoading();
                }
            };
            reader.readAsText(file);
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('mainContent').classList.add('hidden');
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('mainContent').classList.remove('hidden');
        }

        // 处理数据
        function processData(data) {
            rawData = data;
            filteredData = [...rawData];

            // 更新KPI
            updateKPIs();

            // 创建图表
            createCharts();

            // 更新表格
            updateTable();

            // 初始化筛选器
            initializeFilters();

            hideLoading();
        }

        // 更新KPI指标
        function updateKPIs() {
            const totalDocs = rawData.length;

            // 修正字段映射 - 检查实际的数据结构
            console.log('Sample data:', rawData[0]); // 调试用

            // 尝试多种可能的字段名
            const getFieldValue = (item, fieldNames) => {
                for (let field of fieldNames) {
                    if (item[field] !== undefined && item[field] !== null && item[field] !== '') {
                        return item[field];
                    }
                }
                return null;
            };

            // 获取发布单位
            const units = [...new Set(rawData.map(item => {
                const data = item.raw_data || item;
                return data.SUBCOMPANYNAME || '未知';
            }).filter(unit => unit && unit !== '未知'))];
            const totalUnits = units.length;

            // 获取内容长度
            const contentLengths = rawData.map(item => {
                const data = item.raw_data || item;
                const content = data.DOCCONTENT || '';
                return content.length;
            });
            const avgContentLength = contentLengths.length > 0 ?
                Math.round(contentLengths.reduce((a, b) => a + b, 0) / contentLengths.length) : 0;

            // 获取日期
            const dates = rawData.map(item => {
                const data = item.raw_data || item;
                return data.DOCVALIDDATE;
            }).filter(date => date);

            let latestDate = '-';
            if (dates.length > 0) {
                try {
                    const validDates = dates.map(d => new Date(d)).filter(d => !isNaN(d));
                    if (validDates.length > 0) {
                        latestDate = new Date(Math.max(...validDates)).toLocaleDateString();
                    }
                } catch (e) {
                    console.error('Date parsing error:', e);
                }
            }

            document.getElementById('totalDocs').textContent = totalDocs.toLocaleString();
            document.getElementById('totalUnits').textContent = totalUnits;
            document.getElementById('avgContentLength').textContent = avgContentLength.toLocaleString();
            document.getElementById('latestDate').textContent = latestDate;
        }

        // 创建图表
        function createCharts() {
            createUnitChart();
            createTimeChart();
            createLengthChart();
            createWordCloud();
        }

        // 发布单位分布图
        function createUnitChart() {
            const ctx = document.getElementById('unitChart').getContext('2d');

            const getFieldValue = (item, fieldNames) => {
                for (let field of fieldNames) {
                    if (item[field] !== undefined && item[field] !== null && item[field] !== '') {
                        return item[field];
                    }
                }
                return null;
            };

            const unitCounts = {};
            rawData.forEach(item => {
                const data = item.raw_data || item;
                const unit = data.SUBCOMPANYNAME || '未知';
                unitCounts[unit] = (unitCounts[unit] || 0) + 1;
            });

            const sortedUnits = Object.entries(unitCounts)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 10); // 取前10个

            if (charts.unitChart) {
                charts.unitChart.destroy();
            }

            charts.unitChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: sortedUnits.map(item => item[0]),
                    datasets: [{
                        data: sortedUnits.map(item => item[1]),
                        backgroundColor: [
                            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                            '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF',
                            '#4BC0C0', '#FF6384'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed * 100) / total).toFixed(1);
                                    return `${context.label}: ${context.parsed} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // 时间趋势图
        function createTimeChart() {
            const ctx = document.getElementById('timeChart').getContext('2d');

            const getFieldValue = (item, fieldNames) => {
                for (let field of fieldNames) {
                    if (item[field] !== undefined && item[field] !== null && item[field] !== '') {
                        return item[field];
                    }
                }
                return null;
            };

            const dateCounts = {};
            rawData.forEach(item => {
                const data = item.raw_data || item;
                const dateValue = data.DOCVALIDDATE;
                if (dateValue) {
                    try {
                        const date = new Date(dateValue).toISOString().split('T')[0];
                        if (!isNaN(new Date(date))) {
                            dateCounts[date] = (dateCounts[date] || 0) + 1;
                        }
                    } catch (e) {
                        console.error('Date parsing error:', e);
                    }
                }
            });

            const sortedDates = Object.entries(dateCounts)
                .sort((a, b) => new Date(a[0]) - new Date(b[0]))
                .slice(-30); // 最近30天

            if (charts.timeChart) {
                charts.timeChart.destroy();
            }

            // 如果没有日期数据，显示提示
            if (sortedDates.length === 0) {
                ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
                ctx.font = '16px Arial';
                ctx.fillStyle = '#999';
                ctx.textAlign = 'center';
                ctx.fillText('暂无日期数据', ctx.canvas.width / 2, ctx.canvas.height / 2);
                return;
            }

            charts.timeChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: sortedDates.map(item => new Date(item[0]).toLocaleDateString()),
                    datasets: [{
                        label: '发布数量',
                        data: sortedDates.map(item => item[1]),
                        borderColor: '#36A2EB',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#36A2EB',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        }
                    }
                }
            });
        }

        // 内容长度分布图
        function createLengthChart() {
            const ctx = document.getElementById('lengthChart').getContext('2d');

            const getFieldValue = (item, fieldNames) => {
                for (let field of fieldNames) {
                    if (item[field] !== undefined && item[field] !== null && item[field] !== '') {
                        return item[field];
                    }
                }
                return '';
            };

            const lengths = rawData.map(item => {
                const data = item.raw_data || item;
                const content = data.DOCCONTENT || '';
                return content.length;
            });

            const ranges = [
                { label: '0-500字', min: 0, max: 500 },
                { label: '500-1000字', min: 500, max: 1000 },
                { label: '1000-2000字', min: 1000, max: 2000 },
                { label: '2000-5000字', min: 2000, max: 5000 },
                { label: '5000字以上', min: 5000, max: Infinity }
            ];

            const rangeCounts = ranges.map(range => ({
                label: range.label,
                count: lengths.filter(len => len >= range.min && len < range.max).length
            }));

            if (charts.lengthChart) {
                charts.lengthChart.destroy();
            }

            charts.lengthChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: rangeCounts.map(item => item.label),
                    datasets: [{
                        label: '文档数量',
                        data: rangeCounts.map(item => item.count),
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 205, 86, 0.8)',
                            'rgba(75, 192, 192, 0.8)',
                            'rgba(153, 102, 255, 0.8)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 205, 86, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(153, 102, 255, 1)'
                        ],
                        borderWidth: 2,
                        borderRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // 词云图
        function createWordCloud() {
            const container = document.getElementById('wordcloud');
            container.innerHTML = '';

            // 提取关键词
            const allText = rawData.map(item => {
                const data = item.raw_data || item;
                const title = data.DOCSUBJECT || '';
                const content = data.DOCCONTENT || '';
                return title + ' ' + content;
            }).join(' ');

            console.log('All text length:', allText.length);
            console.log('Sample text:', allText.substring(0, 200));

            const words = extractKeywords(allText);
            console.log('Extracted words:', words.slice(0, 10));

            if (words.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #999; padding: 50px;">暂无关键词数据</p>';
                return;
            }

            // 总是使用降级显示，确保能看到内容
            const wordList = words.slice(0, 30).map(([word, count]) => {
                const fontSize = Math.min(count * 3 + 14, 32);
                const colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40', '#E74C3C', '#2ECC71', '#F39C12', '#8E44AD'];
                const color = colors[Math.floor(Math.random() * colors.length)];
                return `<span style="font-size: ${fontSize}px; margin: 8px; color: ${color}; font-weight: bold; display: inline-block; padding: 4px 8px; border-radius: 4px; background: rgba(255,255,255,0.1);">${word}<small style="font-size: 12px; opacity: 0.7;">(${count})</small></span>`;
            }).join(' ');

            container.innerHTML = `
                <div style="text-align: center; padding: 20px; line-height: 2.5; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); border-radius: 10px;">
                    <h4 style="margin-bottom: 20px; color: #2c3e50;">高频关键词</h4>
                    ${wordList}
                </div>
            `;

            // 如果WordCloud库可用，尝试创建真正的词云
            if (typeof WordCloud !== 'undefined') {
                setTimeout(() => {
                    try {
                        const cloudContainer = document.createElement('div');
                        cloudContainer.style.cssText = 'width: 100%; height: 300px; position: relative; margin-top: 20px;';
                        container.appendChild(cloudContainer);

                        WordCloud(cloudContainer, {
                            list: words.slice(0, 50),
                            gridSize: 8,
                            weightFactor: function(size) {
                                return size * 3;
                            },
                            fontFamily: 'Microsoft YaHei, Arial, sans-serif',
                            color: function() {
                                const colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'];
                                return colors[Math.floor(Math.random() * colors.length)];
                            },
                            rotateRatio: 0.3,
                            rotationSteps: 2,
                            backgroundColor: 'transparent',
                            minSize: 12,
                            drawOutOfBound: false
                        });
                        console.log('WordCloud created successfully');
                    } catch (e) {
                        console.error('WordCloud creation failed:', e);
                    }
                }, 100);
            }
        }

        // 提取关键词
        function extractKeywords(text) {
            // 智能检测代码类词汇
            function isCodeLikeWord(word) {
                // 检查是否包含驼峰命名（如：fontSize, backgroundColor）
                if (/[a-z][A-Z]/.test(word)) {
                    return true;
                }

                // 检查是否全大写（如：HTML, CSS, XML）
                if (word.length > 2 && word === word.toUpperCase() && /^[A-Z]+$/.test(word)) {
                    return true;
                }

                // 检查是否包含数字和字母混合（如：h1, div2, span3）
                if (/^[a-zA-Z]+\d+$/.test(word) || /^\d+[a-zA-Z]+$/.test(word)) {
                    return true;
                }

                // 检查是否以Mso开头（Microsoft Office样式）
                if (word.startsWith('Mso') || word.startsWith('mso')) {
                    return true;
                }

                // 检查是否包含常见的代码模式
                const codePatterns = [
                    /^[a-z]+[A-Z][a-z]*/, // 驼峰命名
                    /^[A-Z][a-z]*[A-Z]/, // Pascal命名
                    /^[a-z]+_[a-z]+/, // 下划线命名
                    /^[a-z]+-[a-z]+/, // 连字符命名
                    /^\w*\d+\w*$/, // 包含数字
                    /^[A-Z]{2,}$/, // 全大写缩写
                    /^(webkit|moz|ms|o)-/, // 浏览器前缀
                    /^(data|aria)-/, // HTML属性前缀
                ];

                return codePatterns.some(pattern => pattern.test(word));
            }
            if (!text || text.length === 0) {
                return [];
            }

            // 如果文本太大，截取前50000字符避免栈溢出
            if (text.length > 50000) {
                console.log('Text too large, truncating to 50000 characters');
                text = text.substring(0, 50000);
            }

            // 完善的停用词列表
            const stopWords = new Set([
                // 基础停用词
                '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '那', '他', '她', '它', '我们', '你们', '他们', '这个', '那个', '什么', '怎么', '为什么', '因为', '所以', '但是', '如果', '虽然', '然后', '现在', '已经', '还是', '可以', '应该', '需要', '希望', '觉得', '知道', '认为', '发现', '开始', '继续', '完成', '进行', '实现', '提高', '加强', '推进', '落实', '贯彻', '执行', '实施', '开展', '组织', '参与', '支持', '配合', '协调', '沟通', '交流', '合作', '共同', '一起', '同时', '另外', '此外', '而且', '不仅', '还有', '包括', '涉及', '关于', '对于', '根据', '按照', '依据', '通过', '采用', '利用', '运用', '使用',

                // 字体和格式相关
                '宋体', '仿宋', '微软雅黑', '黑体', '楷体', '小标宋', '方正小标宋简体', '方正小标宋', '华文', '隶书', '幼圆', '新宋体', '细明体', '标楷体', 'Arial', 'Times', 'Helvetica', 'Calibri', 'Verdana',

                // 文档格式相关
                '申请书', '活页', '附件', '联系人', '联系电话', '手机', '电话', '传真', '邮箱', '地址', '邮编', '网址', '网站', '链接', '下载', '上传', '打印', '复印', '扫描', '文件', '文档', '表格', '图片', '照片', '视频', '音频',

                // HTML、CSS和技术相关
                'html', 'div', 'span', 'class', 'style', 'font', 'size', 'color', 'width', 'height', 'margin', 'padding', 'border', 'background', 'text', 'align', 'center', 'left', 'right', 'top', 'bottom', 'px', 'pt', 'em', 'rem', 'css', 'javascript', 'js', 'php', 'asp', 'jsp', 'xml', 'json', 'http', 'https', 'www', 'com', 'cn', 'org', 'net', 'edu', 'gov', 'mso', 'family', 'solid', 'letter', 'windowtext', 'spacing', 'justify', 'line', 'alt', 'New', 'Roman', 'face', 'indent', 'Times', 'normal', 'none', 'auto', 'inherit', 'initial', 'unset', 'serif', 'sans', 'monospace', 'bold', 'italic', 'underline', 'overline', 'strikethrough', 'baseline', 'sub', 'super', 'middle', 'vertical', 'horizontal', 'absolute', 'relative', 'fixed', 'static', 'block', 'inline', 'flex', 'grid', 'table', 'cell', 'row', 'column', 'wrap', 'nowrap', 'break', 'word', 'char', 'ellipsis', 'clip', 'visible', 'hidden', 'scroll', 'overflow', 'content', 'box', 'sizing', 'border', 'outline', 'shadow', 'radius', 'image', 'repeat', 'position', 'attachment', 'origin', 'clip', 'size', 'linear', 'radial', 'gradient', 'rgba', 'hsla', 'transparent', 'opacity', 'filter', 'transform', 'transition', 'animation', 'keyframes', 'ease', 'linear', 'cubic', 'bezier', 'steps', 'infinite', 'alternate', 'reverse', 'forwards', 'backwards', 'both', 'running', 'paused',

                // Microsoft Office和Word格式相关
                'MsoNormal', 'widow', 'orphan', 'pagination', 'ideograph', 'layout', 'inter', 'quot', 'spacerun', 'yes', 'fareast', 'MsoBodyText', 'MsoHeader', 'MsoFooter', 'MsoCaption', 'MsoTitle', 'MsoSubtitle', 'MsoQuote', 'MsoIntenseQuote', 'MsoListParagraph', 'MsoPlainText', 'MsoHtmlAcronym', 'MsoHtmlAddress', 'MsoHtmlCite', 'MsoHtmlCode', 'MsoHtmlDefinition', 'MsoHtmlKeyboard', 'MsoHtmlPreformatted', 'MsoHtmlSample', 'MsoHtmlVariable', 'MsoNormalTable', 'MsoTableGrid', 'MsoTableClassic', 'MsoTableColorful', 'MsoTableColumns', 'MsoTableContemporary', 'MsoTableElegant', 'MsoTableList', 'MsoTableProfessional', 'MsoTableSubtle', 'MsoTableWeb',

                // 编程和代码相关
                'function', 'var', 'let', 'const', 'if', 'else', 'for', 'while', 'do', 'switch', 'case', 'break', 'continue', 'return', 'try', 'catch', 'finally', 'throw', 'new', 'this', 'super', 'extends', 'implements', 'interface', 'abstract', 'static', 'final', 'private', 'public', 'protected', 'package', 'import', 'export', 'default', 'from', 'as', 'typeof', 'instanceof', 'in', 'of', 'delete', 'void', 'null', 'undefined', 'true', 'false', 'NaN', 'Infinity', 'Array', 'Object', 'String', 'Number', 'Boolean', 'Date', 'RegExp', 'Error', 'Math', 'JSON', 'console', 'window', 'document', 'navigator', 'location', 'history', 'screen', 'localStorage', 'sessionStorage', 'setTimeout', 'setInterval', 'clearTimeout', 'clearInterval', 'addEventListener', 'removeEventListener', 'preventDefault', 'stopPropagation', 'getElementById', 'getElementsByClassName', 'getElementsByTagName', 'querySelector', 'querySelectorAll', 'createElement', 'appendChild', 'removeChild', 'insertBefore', 'replaceChild', 'cloneNode', 'setAttribute', 'getAttribute', 'removeAttribute', 'hasAttribute', 'classList', 'className', 'innerHTML', 'outerHTML', 'textContent', 'innerText', 'value', 'checked', 'selected', 'disabled', 'readonly', 'required', 'placeholder', 'maxlength', 'minlength', 'pattern', 'title', 'alt', 'src', 'href', 'target', 'rel', 'type', 'method', 'action', 'enctype', 'autocomplete', 'autofocus', 'multiple', 'accept', 'capture', 'form', 'formaction', 'formenctype', 'formmethod', 'formnovalidate', 'formtarget', 'name', 'id', 'data', 'aria', 'role', 'tabindex', 'accesskey', 'contenteditable', 'draggable', 'dropzone', 'hidden', 'lang', 'spellcheck', 'translate', 'dir', 'contextmenu',

                // 标点和符号
                '、', '。', '，', '；', '：', '？', '！', '（', '）', '【', '】', '《', '》', '〈', '〉', '『', '』', '「', '」', '＜', '＞', '［', '］', '｛', '｝', '…', '——', '－', '＋', '＝', '＊', '／', '＼', '｜', '￥', '％', '＃', '＠', '＆', '＾', '～', '｀',

                // 数字和单位
                '一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '百', '千', '万', '亿', '零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖', '拾', '佰', '仟', '萬', '億', '年', '月', '日', '时', '分', '秒', '点', '号', '第', '次', '个', '位', '名', '项', '条', '款', '章', '节', '段', '页', '行', '列', '栏', '版', '期', '届', '级', '班', '组', '队', '部', '科', '处', '室', '司', '局', '厅', '院', '校', '系', '所', '中心', '办公室',

                // 常见无意义词汇
                '建设', '发展', '改革', '创新', '管理', '服务', '工作', '任务', '目标', '计划', '方案', '措施', '办法', '制度', '规定', '要求', '标准', '原则', '基础', '条件', '环境', '情况', '问题', '困难', '挑战', '机遇', '优势', '特点', '特色', '重点', '关键', '核心', '主要', '重要', '必要', '有效', '成功', '优秀', '先进', '科学', '合理', '正确', '及时', '全面', '深入', '具体', '明确', '清楚', '详细', '简单', '复杂', '困难', '容易', '方便', '安全', '稳定', '可靠', '持续', '长期', '短期', '当前', '未来', '过去', '历史', '传统', '现代'
            ]);

            // 改进的分词方法，分批处理避免栈溢出
            const wordCount = {};
            const chunkSize = 1000; // 每次处理1000字符

            for (let i = 0; i < text.length; i += chunkSize) {
                const chunk = text.substring(i, i + chunkSize);

                // 提取中文词汇（2-4字）
                const chineseMatches = chunk.match(/[\u4e00-\u9fa5]{2,4}/g) || [];

                // 提取英文单词
                const englishMatches = chunk.match(/[a-zA-Z]{3,}/g) || [];

                // 合并并统计
                const words = [...chineseMatches, ...englishMatches];
                words.forEach(word => {
                    const cleanWord = word.trim();

                    // 基本长度和停用词检查
                    if (cleanWord.length < 2 || stopWords.has(cleanWord)) {
                        return;
                    }

                    // 智能过滤：排除看起来像代码的词汇
                    if (isCodeLikeWord(cleanWord)) {
                        return;
                    }

                    wordCount[cleanWord] = (wordCount[cleanWord] || 0) + 1;
                });
            }

            // 转换为词云格式并排序，只保留出现频率>=2的词
            return Object.entries(wordCount)
                .filter(([word, count]) => count >= 2)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 100)
                .map(([word, count]) => [word, count]);
        }

        // 初始化筛选器
        function initializeFilters() {
            const unitFilter = document.getElementById('unitFilter');
            const searchInput = document.getElementById('searchInput');
            const dateFilter = document.getElementById('dateFilter');

            const getFieldValue = (item, fieldNames) => {
                for (let field of fieldNames) {
                    if (item[field] !== undefined && item[field] !== null && item[field] !== '') {
                        return item[field];
                    }
                }
                return null;
            };

            // 填充发布单位选项
            const units = [...new Set(rawData.map(item => {
                const data = item.raw_data || item;
                return data.SUBCOMPANYNAME || '未知';
            }).filter(unit => unit && unit !== '未知'))];

            unitFilter.innerHTML = '<option value="">所有发布单位</option>';
            units.forEach(unit => {
                const option = document.createElement('option');
                option.value = unit;
                option.textContent = unit;
                unitFilter.appendChild(option);
            });

            // 绑定事件
            searchInput.addEventListener('input', applyFilters);
            unitFilter.addEventListener('change', applyFilters);
            dateFilter.addEventListener('change', applyFilters);
        }

        // 应用筛选
        function applyFilters() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const selectedUnit = document.getElementById('unitFilter').value;
            const selectedDateRange = document.getElementById('dateFilter').value;

            const getFieldValue = (item, fieldNames) => {
                for (let field of fieldNames) {
                    if (item[field] !== undefined && item[field] !== null && item[field] !== '') {
                        return item[field];
                    }
                }
                return null;
            };

            filteredData = rawData.filter(item => {
                // 搜索筛选
                const data = item.raw_data || item;
                const title = data.DOCSUBJECT || '';
                const content = data.DOCCONTENT || '';
                const searchMatch = !searchTerm ||
                    title.toLowerCase().includes(searchTerm) ||
                    content.toLowerCase().includes(searchTerm);

                // 单位筛选
                const unit = data.SUBCOMPANYNAME || '未知';
                const unitMatch = !selectedUnit || unit === selectedUnit;

                // 日期筛选
                let dateMatch = true;
                if (selectedDateRange) {
                    const dateValue = data.DOCVALIDDATE;
                    if (dateValue) {
                        try {
                            const itemDate = new Date(dateValue);
                            const now = new Date();
                            const daysAgo = parseInt(selectedDateRange);
                            const cutoffDate = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000);
                            dateMatch = itemDate >= cutoffDate;
                        } catch (e) {
                            dateMatch = true; // 如果日期解析失败，不过滤
                        }
                    }
                }

                return searchMatch && unitMatch && dateMatch;
            });

            currentPage = 1;
            updateTable();
        }

        // 更新表格
        function updateTable() {
            const tableBody = document.getElementById('tableBody');
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageData = filteredData.slice(startIndex, endIndex);

            // 调试：打印第一条数据的结构
            if (pageData.length > 0) {
                console.log('Sample data structure:', pageData[0]);
                console.log('Available fields:', Object.keys(pageData[0]));
                if (pageData[0].raw_data) {
                    console.log('Raw data structure:', pageData[0].raw_data);
                    console.log('Raw data fields:', Object.keys(pageData[0].raw_data));
                }
            }

            tableBody.innerHTML = '';

            pageData.forEach((item, index) => {
                // 从raw_data中获取真实数据
                const rawData = item.raw_data || item;

                const docId = rawData.ID ? parseInt(rawData.ID) : (startIndex + index + 1);
                const docTitle = rawData.DOCSUBJECT || '无标题';
                const docUnit = rawData.SUBCOMPANYNAME || '未知';
                const docDate = rawData.DOCVALIDDATE;
                const content = rawData.DOCCONTENT || '';
                const author = rawData.LASTNAME || '未知';

                console.log(`Row ${index}:`, {
                    ID: rawData.ID,
                    DOCSUBJECT: rawData.DOCSUBJECT,
                    SUBCOMPANYNAME: rawData.SUBCOMPANYNAME,
                    LASTNAME: rawData.LASTNAME,
                    DOCVALIDDATE: rawData.DOCVALIDDATE
                });

                let formattedDate = '-';
                if (docDate) {
                    try {
                        formattedDate = new Date(docDate).toLocaleDateString();
                    } catch (e) {
                        formattedDate = docDate.toString().substring(0, 10);
                    }
                }

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${docId}</td>
                    <td title="${docTitle}">${truncateText(docTitle, 30)}</td>
                    <td>${docUnit}</td>
                    <td>${author}</td>
                    <td>${formattedDate}</td>
                    <td>${content.length.toLocaleString()}</td>
                    <td>
                        <button onclick="viewDocument(${index}, ${startIndex})" style="padding: 5px 10px; background: #3498db; color: white; border: none; border-radius: 3px; cursor: pointer;">查看</button>
                    </td>
                `;
                tableBody.appendChild(row);
            });

            updatePagination();
        }

        // 截断文本
        function truncateText(text, maxLength) {
            return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
        }

        // 更新分页
        function updatePagination() {
            const pagination = document.getElementById('pagination');
            const totalPages = Math.ceil(filteredData.length / itemsPerPage);

            pagination.innerHTML = '';

            // 上一页
            const prevBtn = document.createElement('button');
            prevBtn.textContent = '上一页';
            prevBtn.disabled = currentPage === 1;
            prevBtn.onclick = () => {
                if (currentPage > 1) {
                    currentPage--;
                    updateTable();
                }
            };
            pagination.appendChild(prevBtn);

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.textContent = i;
                pageBtn.className = i === currentPage ? 'active' : '';
                pageBtn.onclick = () => {
                    currentPage = i;
                    updateTable();
                };
                pagination.appendChild(pageBtn);
            }

            // 下一页
            const nextBtn = document.createElement('button');
            nextBtn.textContent = '下一页';
            nextBtn.disabled = currentPage === totalPages;
            nextBtn.onclick = () => {
                if (currentPage < totalPages) {
                    currentPage++;
                    updateTable();
                }
            };
            pagination.appendChild(nextBtn);

            // 显示统计信息
            const info = document.createElement('span');
            info.style.marginLeft = '20px';
            info.style.color = '#7f8c8d';
            info.textContent = `共 ${filteredData.length} 条记录，第 ${currentPage} / ${totalPages} 页`;
            pagination.appendChild(info);
        }

        // 查看文档详情
        function viewDocument(pageIndex, startIndex) {
            const globalIndex = startIndex + pageIndex;
            const doc = filteredData[globalIndex];
            if (!doc) {
                alert('文档未找到');
                return;
            }

            const getFieldValue = (item, fieldNames) => {
                for (let field of fieldNames) {
                    if (item[field] !== undefined && item[field] !== null && item[field] !== '') {
                        return item[field];
                    }
                }
                return null;
            };

            const data = doc.raw_data || doc;
            const docId = data.ID ? parseInt(data.ID) : '-';
            const docTitle = data.DOCSUBJECT || '无标题';
            const docUnit = data.SUBCOMPANYNAME || '未知';
            const docDate = data.DOCVALIDDATE;
            const content = data.DOCCONTENT || '无内容';
            const author = data.LASTNAME || '未知';

            let formattedDate = '-';
            if (docDate) {
                try {
                    formattedDate = new Date(docDate).toLocaleDateString();
                } catch (e) {
                    formattedDate = docDate.toString();
                }
            }

            // 创建模态框
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                z-index: 1000;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 20px;
            `;

            const contentDiv = document.createElement('div');
            contentDiv.style.cssText = `
                background: white;
                border-radius: 15px;
                padding: 30px;
                max-width: 800px;
                max-height: 80vh;
                overflow-y: auto;
                position: relative;
            `;

            contentDiv.innerHTML = `
                <button onclick="this.closest('.modal').remove()" style="position: absolute; top: 15px; right: 15px; background: #e74c3c; color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer;">×</button>
                <h2 style="color: #2c3e50; margin-bottom: 20px;">${docTitle}</h2>
                <div style="margin-bottom: 15px; color: #7f8c8d;">
                    <strong>文档ID:</strong> ${docId} |
                    <strong>发布单位:</strong> ${docUnit} |
                    <strong>发布人:</strong> ${author} |
                    <strong>发布日期:</strong> ${formattedDate}
                </div>
                <div style="line-height: 1.6; color: #333;">
                    ${content.replace(/\n/g, '<br>')}
                </div>
            `;

            modal.className = 'modal';
            modal.appendChild(contentDiv);
            document.body.appendChild(modal);

            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }
    </script>
</body>
</html>
