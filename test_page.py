#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试可视化页面是否能正确加载
"""

import requests
import time

def test_page():
    """测试页面加载"""
    try:
        # 测试页面是否可访问
        response = requests.get('http://localhost:8001/oa_visualization.html', timeout=5)
        if response.status_code == 200:
            print("OK 页面可以访问")
            print(f"页面大小: {len(response.text)} 字符")
        else:
            print(f"ERROR 页面访问失败: {response.status_code}")
            return False
        
        # 测试数据文件是否可访问
        for filename in ['oa_data_clean.json', 'oa_data.json']:
            try:
                data_response = requests.get(f'http://localhost:8001/{filename}', timeout=5)
                if data_response.status_code == 200:
                    print(f"OK {filename} 可以访问")
                    print(f"文件大小: {len(data_response.text)} 字符")
                    
                    # 尝试解析JSON
                    import json
                    data = json.loads(data_response.text)
                    print(f"JSON有效，包含 {len(data)} 条记录")
                    break
                else:
                    print(f"WARNING {filename} 无法访问: {data_response.status_code}")
            except Exception as e:
                print(f"ERROR 测试 {filename} 时出错: {e}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("ERROR 无法连接到服务器，请确保服务器正在运行")
        return False
    except Exception as e:
        print(f"ERROR 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("测试可视化页面...")
    print("=" * 30)
    
    if test_page():
        print("\n测试完成！")
        print("请在浏览器中访问: http://localhost:8001/oa_visualization.html")
        print("然后点击'加载数据'按钮测试数据加载功能")
    else:
        print("\n测试失败！请检查服务器是否正在运行")

if __name__ == "__main__":
    main()
